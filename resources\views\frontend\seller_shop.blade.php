@php
    $theme = get_setting('homepage_select');
@endphp

@extends('frontend.'.$theme.'.layouts.app')

@section('meta_title'){{ $shop->meta_title }}@stop

@section('meta_description'){{ $shop->meta_description }}@stop

@section('meta')
    <!-- Schema.org markup for Google+ -->
    <meta itemprop="name" content="{{ $shop->meta_title }}">
    <meta itemprop="description" content="{{ $shop->meta_description }}">
    <meta itemprop="image" content="{{ uploaded_asset($shop->logo) }}">

    <!-- Twitter Card data -->
    <meta name="twitter:card" content="website">
    <meta name="twitter:site" content="@publisher_handle">
    <meta name="twitter:title" content="{{ $shop->meta_title }}">
    <meta name="twitter:description" content="{{ $shop->meta_description }}">
    <meta name="twitter:creator" content="@author_handle">
    <meta name="twitter:image" content="{{ uploaded_asset($shop->meta_img) }}">

    <!-- Open Graph data -->
    <meta property="og:title" content="{{ $shop->meta_title }}" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="{{ route('shop.visit', $shop->slug) }}" />
    <meta property="og:image" content="{{ uploaded_asset($shop->logo) }}" />
    <meta property="og:description" content="{{ $shop->meta_description }}" />
    <meta property="og:site_name" content="{{ $shop->name }}" />
@endsection

@section('content')

    <!-- Top Banner Section -->
    @if ($shop->top_banner)
        <section class="mb-3">
            <div class="container-fluid px-0">
                <div class="position-relative">
                    <img class="d-block w-100 lazyload"
                         style="height: 200px; object-fit: cover;"
                         src="{{ static_asset('assets/img/placeholder-rect.jpg') }}"
                         data-src="{{ uploaded_asset($shop->top_banner) }}"
                         alt="{{ $shop->name }} banner">
                    <div class="position-absolute" style="top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, rgba(0,0,0,0.3), transparent);"></div>
                </div>
            </div>
        </section>
    @endif

    <!-- Shop Header Section -->
    <section class="py-4 bg-white border-bottom">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="d-flex align-items-center">
                        <!-- Shop Logo -->
                        <div class="position-relative mr-4">
                            <div class="shop-logo-container" style="width: 120px; height: 120px; border-radius: 15px; overflow: hidden; border: 3px solid #f8f9fa; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                                @if($shop->logo)
                                    <img class="lazyload w-100 h-100"
                                         style="object-fit: cover;"
                                         src="{{ static_asset('assets/img/placeholder.jpg') }}"
                                         data-src="{{ uploaded_asset($shop->logo) }}"
                                         alt="{{ $shop->name }}"
                                         onerror="this.onerror=null;this.src='{{ static_asset('assets/img/placeholder.jpg') }}';">
                                @else
                                    @php
                                        $randomColor = sprintf('#%06X', mt_rand(0, 0xFFFFFF));
                                        $firstLetter = strtoupper(substr($shop->name, 0, 1));
                                    @endphp
                                    <div class="d-flex align-items-center justify-content-center w-100 h-100" style="background-color: {{ $randomColor }};">
                                        <span style="font-size: 3rem; color: white; font-weight: bold;">{{ $firstLetter }}</span>
                                    </div>
                                @endif
                            </div>
                            @if ($shop->verification_status == 1)
                                <div class="position-absolute" style="bottom: -5px; right: -5px; background: #28a745; border-radius: 50%; padding: 5px;">
                                    <i class="las la-check text-white" style="font-size: 16px;"></i>
                                </div>
                            @endif
                        </div>

                        <!-- Shop Info -->
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <h1 class="fs-24 fw-700 text-dark mb-0 mr-3">{{ $shop->name }}</h1>
                                @if ($shop->verification_status == 1)
                                    <span class="badge badge-success px-3 py-1">{{ translate('Verified Seller') }}</span>
                                @endif
                            </div>

                            @if($shop->address)
                                <p class="text-muted mb-2">
                                    <i class="las la-map-marker-alt mr-1"></i>
                                    {{ $shop->address }}
                                </p>
                            @endif

                            @if($shop->phone)
                                <p class="text-muted mb-2">
                                    <i class="las la-phone mr-1"></i>
                                    {{ $shop->phone }}
                                </p>
                            @endif

                            <!-- Social Media Links -->
                            @if($shop->facebook || $shop->instagram || $shop->twitter || $shop->youtube || $shop->google)
                                <div class="d-flex align-items-center mt-3">
                                    <span class="text-muted mr-3">{{ translate('Follow us:') }}</span>
                                    @if($shop->facebook)
                                        <a href="{{ $shop->facebook }}" target="_blank" class="text-primary mr-3" style="font-size: 20px;">
                                            <i class="lab la-facebook"></i>
                                        </a>
                                    @endif
                                    @if($shop->instagram)
                                        <a href="{{ $shop->instagram }}" target="_blank" class="text-danger mr-3" style="font-size: 20px;">
                                            <i class="lab la-instagram"></i>
                                        </a>
                                    @endif
                                    @if($shop->twitter)
                                        <a href="{{ $shop->twitter }}" target="_blank" class="text-info mr-3" style="font-size: 20px;">
                                            <i class="lab la-twitter"></i>
                                        </a>
                                    @endif
                                    @if($shop->youtube)
                                        <a href="{{ $shop->youtube }}" target="_blank" class="text-danger mr-3" style="font-size: 20px;">
                                            <i class="lab la-youtube"></i>
                                        </a>
                                    @endif
                                    @if($shop->google)
                                        <a href="{{ $shop->google }}" target="_blank" class="text-warning mr-3" style="font-size: 20px;">
                                            <i class="lab la-google"></i>
                                        </a>
                                    @endif
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 text-lg-right mt-3 mt-lg-0">
                    <!-- Shop Stats -->
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="border-right">
                                <h4 class="fs-18 fw-700 text-primary mb-0">{{ $shop->user->products->where('published', 1)->where('approved', 1)->count() }}</h4>
                                <small class="text-muted">{{ translate('Products') }}</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="border-right">
                                <h4 class="fs-18 fw-700 text-success mb-0">{{ $shop->followers->count() }}</h4>
                                <small class="text-muted">{{ translate('Followers') }}</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <h4 class="fs-18 fw-700 text-warning mb-0">{{ number_format($shop->user->products->where('published', 1)->where('approved', 1)->avg('rating') ?? 0, 1) }}</h4>
                            <small class="text-muted">{{ translate('Rating') }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Navigation Menu -->
    <section class="bg-light border-bottom">
        <div class="container">
            <div class="d-flex flex-wrap justify-content-center justify-content-md-start py-3">
                <a class="nav-link-shop mr-4 @if(!isset($type)) active @endif"
                   href="{{ route('shop.visit', $shop->slug) }}">
                    <i class="las la-home mr-2"></i>{{ translate('Store Home')}}
                </a>
                <a class="nav-link-shop mr-4 @if(isset($type) && $type == 'all-products') active @endif"
                   href="{{ route('shop.visit.type', ['slug'=>$shop->slug, 'type'=>'all-products']) }}">
                    <i class="las la-th-large mr-2"></i>{{ translate('All Products')}}
                </a>
                <a class="nav-link-shop mr-4 @if(isset($type) && $type == 'top-selling') active @endif"
                   href="{{ route('shop.visit.type', ['slug'=>$shop->slug, 'type'=>'top-selling']) }}">
                    <i class="las la-fire mr-2"></i>{{ translate('Top Selling')}}
                </a>
                @php
                    $coupons = get_coupons($shop->user->id);
                @endphp
                @if (count($coupons) > 0)
                    <a class="nav-link-shop mr-4 @if(isset($type) && $type == 'cupons') active @endif"
                       href="{{ route('shop.visit.type', ['slug'=>$shop->slug, 'type'=>'cupons']) }}">
                        <i class="las la-tags mr-2"></i>{{ translate('Coupons')}}
                    </a>
                @endif
            </div>
        </div>
    </section>

    @if (!isset($type))
        @php
            $feature_products = $shop->user->products->where('published', 1)->where('approved', 1)->where('seller_featured', 1);
        @endphp
        @if (count($feature_products) > 0)
            <!-- Featured Products -->
            <section class="mt-4 mb-4" id="section_featured">
                <div class="container">
                    <!-- Top Section -->
                    <div class="d-flex mb-4 align-items-baseline justify-content-between">
                        <!-- Title -->
                        <h3 class="fs-20 fw-700 mb-3 mb-sm-0">
                            <span class="text-primary">{{ translate('Featured Products') }}</span>
                        </h3>
                        <!-- Links -->
                        <div class="d-flex">
                            <a type="button" class="arrow-prev slide-arrow text-secondary mr-2" onclick="clickToSlide('slick-prev','section_featured')"><i class="las la-angle-left fs-20 fw-600"></i></a>
                            <a type="button" class="arrow-next slide-arrow text-secondary ml-2" onclick="clickToSlide('slick-next','section_featured')"><i class="las la-angle-right fs-20 fw-600"></i></a>
                        </div>
                    </div>
                    <!-- Products Section -->
                    <div class="bg-white rounded shadow-sm p-3">
                        <div class="row row-cols-xl-4 row-cols-lg-3 row-cols-md-3 row-cols-2">
                            @foreach ($feature_products as $key => $product)
                                <div class="col mb-3">
                                    <div class="product-card h-100">
                                        @include('frontend.'.get_setting('homepage_select').'.partials.product_box_1',['product' => $product])
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </section>
        @endif

        <!-- Banner Slider -->
        @if ($shop->sliders != null)
            <section class="mt-4 mb-4">
                <div class="container">
                    <div class="bg-white rounded shadow-sm overflow-hidden">
                        <div class="aiz-carousel mobile-img-auto-height" data-arrows="true" data-dots="true" data-autoplay="true" data-infinite="true">
                            @foreach (explode(',',$shop->sliders) as $key => $slide)
                                <div class="carousel-box">
                                    <div class="position-relative">
                                        <img class="d-block lazyload w-100"
                                             style="height: 300px; object-fit: cover;"
                                             src="{{ static_asset('assets/img/placeholder-rect.jpg') }}"
                                             data-src="{{ uploaded_asset($slide) }}"
                                             alt="{{ $shop->name }} slider {{ $key + 1 }}">
                                        <div class="position-absolute" style="top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, rgba(0,0,0,0.1), transparent);"></div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </section>
        @endif

        <!-- Coupons -->
        @if (count($coupons)>0)
            <section class="mt-4 mb-4" id="section_coupons">
                <div class="container">
                    <!-- Top Section -->
                    <div class="d-flex mb-4 align-items-baseline justify-content-between">
                        <!-- Title -->
                        <h3 class="fs-20 fw-700 mb-3 mb-sm-0">
                            <span class="text-primary">{{ translate('Available Coupons') }}</span>
                        </h3>
                        <!-- Links -->
                        <div class="d-flex align-items-center">
                            <a type="button" class="arrow-prev slide-arrow text-secondary mr-2" onclick="clickToSlide('slick-prev','section_coupons')"><i class="las la-angle-left fs-20 fw-600"></i></a>
                            <a class="btn btn-sm btn-outline-primary mr-2" href="{{ route('shop.visit.type', ['slug'=>$shop->slug, 'type'=>'cupons']) }}">{{ translate('View All') }}</a>
                            <a type="button" class="arrow-next slide-arrow text-secondary ml-2" onclick="clickToSlide('slick-next','section_coupons')"><i class="las la-angle-right fs-20 fw-600"></i></a>
                        </div>
                    </div>
                    <!-- Coupons Section -->
                    <div class="bg-white rounded shadow-sm p-3">
                        <div class="aiz-carousel sm-gutters-16" data-items="3" data-lg-items="2" data-sm-items="1" data-arrows='true' data-infinite='false'>
                            @foreach ($coupons->take(10) as $key => $coupon)
                                <div class="carousel-box px-2">
                                    <div class="coupon-card">
                                        @include('frontend.'.get_setting('homepage_select').'.partials.coupon_box',['coupon' => $coupon])
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </section>
        @endif

        @if ($shop->banner_full_width_1)
            <!-- Banner full width 1 -->
            <section class="mt-4 mb-4">
                <div class="container">
                    @foreach (explode(',',$shop->banner_full_width_1) as $key => $banner)
                        <div class="bg-white rounded shadow-sm overflow-hidden mb-3">
                            <div class="position-relative">
                                <img class="d-block lazyload w-100"
                                     style="height: 200px; object-fit: cover;"
                                     src="{{ static_asset('assets/img/placeholder-rect.jpg') }}"
                                     data-src="{{ uploaded_asset($banner) }}"
                                     alt="{{ $shop->name }} promotional banner {{ $key + 1 }}">
                                <div class="position-absolute" style="top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, rgba(0,0,0,0.1), transparent);"></div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </section>
        @endif

        @if($shop->banners_half_width)
            <!-- Banner half width -->
            <section class="mt-4 mb-4">
                <div class="container">
                    <div class="row">
                        @foreach (explode(',',$shop->banners_half_width) as $key => $banner)
                            <div class="col-md-6 mb-3">
                                <div class="bg-white rounded shadow-sm overflow-hidden">
                                    <div class="position-relative">
                                        <img class="d-block lazyload w-100"
                                             style="height: 180px; object-fit: cover;"
                                             src="{{ static_asset('assets/img/placeholder-rect.jpg') }}"
                                             data-src="{{ uploaded_asset($banner) }}"
                                             alt="{{ $shop->name }} banner {{ $key + 1 }}">
                                        <div class="position-absolute" style="top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, rgba(0,0,0,0.1), transparent);"></div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </section>
        @endif

    @endif

    <section class="mt-4 mb-4" id="section_types">
        <div class="container">
            <!-- Top Section -->
            <div class="d-flex mb-4 align-items-baseline justify-content-between">
                <!-- Title -->
                <h3 class="fs-20 fw-700 mb-3 mb-sm-0">
                    <span class="text-primary">
                        @if (!isset($type))
                            {{ translate('Latest Products')}}
                        @elseif ($type == 'top-selling')
                            {{ translate('Top Selling Products')}}
                        @elseif ($type == 'cupons')
                            {{ translate('All Available Coupons')}}
                        @endif
                    </span>
                </h3>
                @if (!isset($type))
                    <!-- Links -->
                    <div class="d-flex align-items-center">
                        <a class="btn btn-sm btn-outline-primary mr-2" href="{{ route('shop.visit.type', ['slug'=>$shop->slug, 'type'=>'all-products']) }}">{{ translate('View All Products') }}</a>
                        <a type="button" class="arrow-prev slide-arrow text-secondary mr-2" onclick="clickToSlide('slick-prev','section_types')"><i class="las la-angle-left fs-20 fw-600"></i></a>
                        <a type="button" class="arrow-next slide-arrow text-secondary ml-2" onclick="clickToSlide('slick-next','section_types')"><i class="las la-angle-right fs-20 fw-600"></i></a>
                    </div>
                @endif
            </div>



            @if (!isset($type))
                <!-- Latest Products Section -->
                <div class="bg-white rounded shadow-sm p-3">
                    <div class="row row-cols-xl-4 row-cols-lg-3 row-cols-md-3 row-cols-2">
                        @foreach ($products as $key => $product)
                            <div class="col mb-3">
                                <div class="product-card h-100">
                                    @include('frontend.'.get_setting('homepage_select').'.partials.product_box_1',['product' => $product])
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Pagination for default products -->
                <div class="aiz-pagination mt-4 mb-4">
                    {{ $products->links() }}
                </div>

                @if ($shop->banner_full_width_2)
                    <!-- Banner full width 2 -->
                    <section class="mt-4 mb-4">
                        <div class="container">
                            @foreach (explode(',',$shop->banner_full_width_2) as $key => $banner)
                                <div class="bg-white rounded shadow-sm overflow-hidden mb-3">
                                    <div class="position-relative">
                                        <img class="d-block lazyload w-100"
                                             style="height: 200px; object-fit: cover;"
                                             src="{{ static_asset('assets/img/placeholder-rect.jpg') }}"
                                             data-src="{{ uploaded_asset($banner) }}"
                                             alt="{{ $shop->name }} promotional banner {{ $key + 1 }}">
                                        <div class="position-absolute" style="top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, rgba(0,0,0,0.1), transparent);"></div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </section>
                @endif


            @elseif ($type == 'cupons')
                <!-- All Coupons Section -->
                <div class="bg-white rounded shadow-sm p-3">
                    <div class="row row-cols-xl-3 row-cols-md-2 row-cols-1">
                        @foreach ($coupons as $key => $coupon)
                            <div class="col mb-4">
                                <div class="coupon-card h-100">
                                    @include('frontend.'.get_setting('homepage_select').'.partials.coupon_box',['coupon' => $coupon])
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
                <div class="aiz-pagination mt-4 mb-4">
                    {{ $coupons->links() }}
                </div>

            @elseif ($type == 'all-products')
                <!-- All Products Section -->
                <form class="" id="search-form" action="" method="GET">
                    <div class="row">
                        <!-- Sidebar Filters -->
                        <div class="col-xl-3">
                            <div class="aiz-filter-sidebar collapse-sidebar-wrap sidebar-xl sidebar-right z-1035">
                                <div class="overlay overlay-fixed dark c-pointer" data-toggle="class-toggle" data-target=".aiz-filter-sidebar" data-same=".filter-sidebar-thumb"></div>
                                <div class="collapse-sidebar c-scrollbar-light text-left">
                                    <div class="d-flex d-xl-none justify-content-between align-items-center pl-3 border-bottom">
                                        <h3 class="h6 mb-0 fw-600">{{ translate('Filters') }}</h3>
                                        <button type="button" class="btn btn-sm p-2 filter-sidebar-thumb" data-toggle="class-toggle" data-target=".aiz-filter-sidebar" >
                                            <i class="las la-times la-2x"></i>
                                        </button>
                                    </div>

                                    <!-- Categories -->
                                    <div class="bg-white border mb-3">
                                        <div class="fs-16 fw-700 p-3">
                                            <a href="#collapse_1" class="dropdown-toggle filter-section text-dark d-flex align-items-center justify-content-between" data-toggle="collapse">
                                                {{ translate('Categories')}}
                                            </a>
                                        </div>
                                        <div class="collapse show" id="collapse_1">
                                            <ul class="p-3 mb-0 list-unstyled">
                                                @foreach (get_categories_by_products($shop->user->id) as $category)
                                                    <li class="mb-3 text-dark">
                                                        <label class="aiz-checkbox">
                                                            <input
                                                                type="checkbox"
                                                                name="selected_categories[]"
                                                                value="{{ $category->id }}" @if (in_array($category->id, $selected_categories)) checked @endif
                                                                onchange="filter()"
                                                            >
                                                            <span class="aiz-square-check"></span>
                                                            <span class="fs-14 fw-400 text-dark">{{ $category->getTranslation('name') }}</span>
                                                        </label>
                                                    </li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    </div>

                                    <!-- Price range -->
                                    <div class="bg-white border mb-3">
                                        <div class="fs-16 fw-700 p-3">
                                            {{ translate('Price range')}}
                                        </div>
                                        <div class="p-3 mr-3">
                                            <div class="aiz-range-slider">
                                                <div
                                                    id="input-slider-range"
                                                    data-range-value-min="@if(get_products_count($shop->user->id) < 1) 0 @else {{ get_product_min_unit_price($shop->user->id) }} @endif"
                                                    data-range-value-max="@if(get_products_count($shop->user->id) < 1) 0 @else {{ get_product_max_unit_price($shop->user->id) }} @endif"
                                                ></div>

                                                <div class="row mt-2">
                                                    <div class="col-6">
                                                        <span class="range-slider-value value-low fs-14 fw-600 opacity-70"
                                                              @if ($min_price != null)
                                                                  data-range-value-low="{{ $min_price }}"
                                                              @elseif($products->min('unit_price') > 0)
                                                                  data-range-value-low="{{ $products->min('unit_price') }}"
                                                              @else
                                                                  data-range-value-low="0"
                                                              @endif
                                                              id="input-slider-range-value-low"
                                                        ></span>
                                                    </div>
                                                    <div class="col-6 text-right">
                                                        <span class="range-slider-value value-high fs-14 fw-600 opacity-70"
                                                              @if ($max_price != null)
                                                                  data-range-value-high="{{ $max_price }}"
                                                              @elseif($products->max('unit_price') > 0)
                                                                  data-range-value-high="{{ $products->max('unit_price') }}"
                                                              @else
                                                                  data-range-value-high="0"
                                                              @endif
                                                              id="input-slider-range-value-high"
                                                        ></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- Hidden Items -->
                                        <input type="hidden" name="min_price" value="">
                                        <input type="hidden" name="max_price" value="">
                                    </div>

                                    <!-- Ratings -->
                                    <div class="bg-white border mb-3">
                                        <div class="fs-16 fw-700 p-3">
                                            <a href="#collapse_2" class="dropdown-toggle filter-section text-dark d-flex align-items-center justify-content-between collapsed" data-toggle="collapse" data-target="#collapse_2">
                                                {{ translate('Ratings')}}
                                            </a>
                                        </div>
                                        <div class="collapse" id="collapse_2">
                                            <div class="p-3 aiz-checkbox-list">
                                                <label class="aiz-checkbox mb-3">
                                                    <input
                                                        type="radio"
                                                        name="rating"
                                                        value="5" @if ($rating==5) checked @endif
                                                        onchange="filter()"
                                                    >
                                                    <span class="aiz-square-check"></span>
                                                    <span class="rating rating-mr-1">{{ renderStarRating(5) }}</span>
                                                </label>
                                                <label class="aiz-checkbox mb-3">
                                                    <input
                                                        type="radio"
                                                        name="rating"
                                                        value="4" @if ($rating==4) checked @endif
                                                        onchange="filter()"
                                                    >
                                                    <span class="aiz-square-check"></span>
                                                    <span class="rating rating-mr-1">{{ renderStarRating(4) }}</span>
                                                    <span class="fs-14 fw-400 text-dark">{{ translate('And Up')}}</span>
                                                </label>
                                                <label class="aiz-checkbox mb-3">
                                                    <input
                                                        type="radio"
                                                        name="rating"
                                                        value="3" @if ($rating==3) checked @endif
                                                        onchange="filter()"
                                                    >
                                                    <span class="aiz-square-check"></span>
                                                    <span class="rating rating-mr-1">{{ renderStarRating(3) }}</span>
                                                    <span class="fs-14 fw-400 text-dark">{{ translate('And Up')}}</span>
                                                </label>
                                                <label class="aiz-checkbox mb-3">
                                                    <input
                                                        type="radio"
                                                        name="rating"
                                                        value="2" @if ($rating==2) checked @endif
                                                        onchange="filter()"
                                                    >
                                                    <span class="aiz-square-check"></span>
                                                    <span class="rating rating-mr-1">{{ renderStarRating(2) }}</span>
                                                    <span class="fs-14 fw-400 text-dark">{{ translate('And Up')}}</span>
                                                </label>
                                                <label class="aiz-checkbox mb-3">
                                                    <input
                                                        type="radio"
                                                        name="rating"
                                                        value="1" @if ($rating==1) checked @endif
                                                        onchange="filter()"
                                                    >
                                                    <span class="aiz-square-check"></span>
                                                    <span class="rating rating-mr-1">{{ renderStarRating(1) }}</span>
                                                    <span class="fs-14 fw-400 text-dark">{{ translate('And Up')}}</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Brands -->
                                    <div class="bg-white border mb-3">
                                        <div class="fs-16 fw-700 p-3">
                                            <a href="#collapse_3" class="dropdown-toggle filter-section text-dark d-flex align-items-center justify-content-between collapsed" data-toggle="collapse" data-target="#collapse_3">
                                                {{ translate('Brands')}}
                                            </a>
                                        </div>
                                        <div class="collapse" id="collapse_3">
                                            <div class="p-3 aiz-checkbox-list">
                                                @foreach (get_brands_by_products($shop->user->id) as $key => $brand)
                                                    <label class="aiz-checkbox mb-3">
                                                        <input value="{{ $brand->slug }}" type="radio" onchange="filter()"
                                                               name="brand" @isset($brand_id) @if ($brand_id == $brand->id) checked @endif @endisset>
                                                        <span class="aiz-square-check"></span>
                                                        <span class="fs-14 fw-400 text-dark">{{ $brand->getTranslation('name') }}</span>
                                                    </label>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>

                        <!-- Contents -->
                        <div class="col-xl-9">

                            <!-- Breadcrumb -->
                            <ul class="breadcrumb bg-transparent py-0 px-1">
                                <li class="breadcrumb-item has-transition opacity-50 hov-opacity-100">
                                    <a class="text-reset" href="{{ route('home') }}">{{ translate('Home')}}</a>
                                </li>
                                <li class="breadcrumb-item opacity-50 hov-opacity-100">
                                    <a class="text-reset" href="{{ route('shop.visit', $shop->slug) }}">{{ $shop->name }}</a>
                                </li>
                                <li class="text-dark fw-600 breadcrumb-item">
                                    "{{ translate('All Products') }}"
                                </li>
                            </ul>

                            <!-- Top Filters -->
                            <div class="text-left">
                                <div class="row gutters-5 flex-wrap align-items-center">
                                    <div class="col-lg col-10">
                                        <h1 class="fs-20 fs-md-24 fw-700 text-dark">
                                            {{ translate('All Products') }}
                                        </h1>
                                    </div>
                                    <div class="col-2 col-lg-auto d-xl-none mb-lg-3 text-right">
                                        <button type="button" class="btn btn-icon p-0" data-toggle="class-toggle" data-target=".aiz-filter-sidebar">
                                            <i class="la la-filter la-2x"></i>
                                        </button>
                                    </div>
                                    <div class="col-6 col-lg-auto mb-3 w-lg-200px">
                                        <select class="form-control form-control-sm aiz-selectpicker rounded-0" name="sort_by" onchange="filter()">
                                            <option value="">{{ translate('Sort by')}}</option>
                                            <option value="newest" @isset($sort_by) @if ($sort_by == 'newest') selected @endif @endisset>{{ translate('Newest')}}</option>
                                            <option value="oldest" @isset($sort_by) @if ($sort_by == 'oldest') selected @endif @endisset>{{ translate('Oldest')}}</option>
                                            <option value="price-asc" @isset($sort_by) @if ($sort_by == 'price-asc') selected @endif @endisset>{{ translate('Price low to high')}}</option>
                                            <option value="price-desc" @isset($sort_by) @if ($sort_by == 'price-desc') selected @endif @endisset>{{ translate('Price high to low')}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Products -->
                            <div class="px-3">
                                <div class="row gutters-16 row-cols-xxl-4 row-cols-xl-3 row-cols-lg-4 row-cols-md-3 row-cols-2 border-top border-left">
                                    @foreach ($products as $key => $product)
                                        <div class="col border-right border-bottom has-transition hov-shadow-out z-1">
                                            @include('frontend.'.get_setting('homepage_select').'.partials.product_box_1',['product' => $product])
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            <div class="aiz-pagination mt-4">
                                {{ $products->appends(request()->input())->links() }}
                            </div>
                        </div>
                    </div>
                </form>
            @else
                <!-- Top Selling Products Section -->
                <div class="px-3">
                    <div class="row gutters-16 row-cols-xxl-6 row-cols-xl-5 row-cols-lg-4 row-cols-md-3 row-cols-2 border-left border-top">
                        @foreach ($products as $key => $product)
                            <div class="col border-bottom border-right overflow-hidden has-transition hov-shadow-out z-1">
                                @include('frontend.'.get_setting('homepage_select').'.partials.product_box_1',['product' => $product])
                            </div>
                        @endforeach
                    </div>
                </div>
                <div class="aiz-pagination mt-4 mb-4">
                    {{ $products->links() }}
                </div>
            @endif
        </div>
    </section>

@endsection

@section('css')
<style>
    .nav-link-shop {
        color: #6c757d;
        text-decoration: none;
        padding: 10px 15px;
        border-radius: 25px;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .nav-link-shop:hover {
        color: #007bff;
        background-color: #f8f9fa;
        text-decoration: none;
    }

    .nav-link-shop.active {
        color: #007bff;
        background-color: #e3f2fd;
        font-weight: 600;
    }

    .shop-logo-container {
        transition: transform 0.3s ease;
    }

    .shop-logo-container:hover {
        transform: scale(1.05);
    }

    .product-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .coupon-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .coupon-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.1);
    }

    .carousel-box img {
        transition: transform 0.3s ease;
    }

    .carousel-box:hover img {
        transform: scale(1.02);
    }

    .aiz-carousel .slick-arrow {
        background: #007bff;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        z-index: 10;
    }

    .aiz-carousel .slick-arrow:before {
        color: white;
        font-size: 16px;
    }

    .aiz-carousel .slick-dots li button:before {
        color: #007bff;
        font-size: 12px;
    }

    .aiz-carousel .slick-dots li.slick-active button:before {
        color: #007bff;
    }

    @media (max-width: 768px) {
        .shop-logo-container {
            width: 80px !important;
            height: 80px !important;
        }

        .nav-link-shop {
            padding: 8px 12px;
            font-size: 14px;
        }
    }
</style>
@endsection

@section('script')
    <script type="text/javascript">
        function filter(){
            $('#search-form').submit();
        }

        function rangefilter(arg){
            $('input[name=min_price]').val(arg[0]);
            $('input[name=max_price]').val(arg[1]);
            filter();
        }

        // Enhanced functionality
        $(document).ready(function() {
            // Add loading animation for images
            $('.lazyload').on('load', function() {
                $(this).addClass('loaded');
            });

            // Smooth scroll for navigation
            $('.nav-link-shop').on('click', function(e) {
                if (this.hash !== "") {
                    e.preventDefault();
                    var hash = this.hash;
                    $('html, body').animate({
                        scrollTop: $(hash).offset().top - 100
                    }, 800);
                }
            });
        });
    </script>
@endsection
